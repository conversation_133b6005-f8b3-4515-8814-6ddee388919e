2025/06/18 02:23:18 [notice] 1#1: using the "epoll" event method
2025/06/18 02:23:18 [notice] 1#1: nginx/1.25.5
2025/06/18 02:23:18 [notice] 1#1: built by gcc 13.2.1 20231014 (Alpine 13.2.1_git20231014) 
2025/06/18 02:23:18 [notice] 1#1: OS: Linux 6.6.87.1-microsoft-standard-WSL2
2025/06/18 02:23:18 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/18 02:23:18 [notice] 1#1: start worker processes
2025/06/18 02:23:18 [notice] 1#1: start worker process 29
2025/06/18 02:23:18 [notice] 1#1: start worker process 30
2025/06/18 02:23:18 [notice] 1#1: start worker process 31
2025/06/18 02:23:18 [notice] 1#1: start worker process 32
2025/06/18 02:23:18 [notice] 1#1: start worker process 33
2025/06/18 02:23:18 [notice] 1#1: start worker process 34
2025/06/18 02:23:18 [notice] 1#1: start worker process 35
2025/06/18 02:23:18 [notice] 1#1: start worker process 36
2025/06/18 02:23:18 [notice] 1#1: start worker process 37
2025/06/18 02:23:18 [notice] 1#1: start worker process 38
2025/06/18 02:23:18 [notice] 1#1: start worker process 39
2025/06/18 02:23:18 [notice] 1#1: start worker process 40
2025/06/18 02:23:18 [notice] 1#1: start worker process 41
2025/06/18 02:23:18 [notice] 1#1: start worker process 42
2025/06/18 02:23:18 [notice] 1#1: start worker process 43
2025/06/18 02:23:18 [notice] 1#1: start worker process 44
2025/06/18 04:22:42 [notice] 1#1: using the "epoll" event method
2025/06/18 04:22:42 [notice] 1#1: nginx/1.25.5
2025/06/18 04:22:42 [notice] 1#1: built by gcc 13.2.1 20231014 (Alpine 13.2.1_git20231014) 
2025/06/18 04:22:42 [notice] 1#1: OS: Linux 6.6.87.2-microsoft-standard-WSL2
2025/06/18 04:22:42 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/18 04:22:42 [notice] 1#1: start worker processes
2025/06/18 04:22:42 [notice] 1#1: start worker process 29
2025/06/18 04:22:42 [notice] 1#1: start worker process 30
2025/06/18 04:22:42 [notice] 1#1: start worker process 31
2025/06/18 04:22:42 [notice] 1#1: start worker process 32
2025/06/18 04:22:42 [notice] 1#1: start worker process 33
2025/06/18 04:22:42 [notice] 1#1: start worker process 34
2025/06/18 04:22:42 [notice] 1#1: start worker process 35
2025/06/18 04:22:42 [notice] 1#1: start worker process 36
2025/06/18 04:22:42 [notice] 1#1: start worker process 37
2025/06/18 04:22:42 [notice] 1#1: start worker process 38
2025/06/18 04:22:42 [notice] 1#1: start worker process 39
2025/06/18 04:22:42 [notice] 1#1: start worker process 40
2025/06/18 04:22:42 [notice] 1#1: start worker process 41
2025/06/18 04:22:42 [notice] 1#1: start worker process 42
2025/06/18 04:22:42 [notice] 1#1: start worker process 43
2025/06/18 04:22:42 [notice] 1#1: start worker process 44
2025/06/18 04:48:29 [notice] 1#1: using the "epoll" event method
2025/06/18 04:48:29 [notice] 1#1: nginx/1.25.5
2025/06/18 04:48:29 [notice] 1#1: built by gcc 13.2.1 20231014 (Alpine 13.2.1_git20231014) 
2025/06/18 04:48:29 [notice] 1#1: OS: Linux 6.6.87.2-microsoft-standard-WSL2
2025/06/18 04:48:29 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/18 04:48:29 [notice] 1#1: start worker processes
2025/06/18 04:48:29 [notice] 1#1: start worker process 29
2025/06/18 04:48:29 [notice] 1#1: start worker process 30
2025/06/18 04:48:29 [notice] 1#1: start worker process 31
2025/06/18 04:48:29 [notice] 1#1: start worker process 32
2025/06/18 04:48:29 [notice] 1#1: start worker process 33
2025/06/18 04:48:29 [notice] 1#1: start worker process 34
2025/06/18 04:48:29 [notice] 1#1: start worker process 35
2025/06/18 04:48:29 [notice] 1#1: start worker process 36
2025/06/18 04:48:29 [notice] 1#1: start worker process 37
2025/06/18 04:48:29 [notice] 1#1: start worker process 38
2025/06/18 04:48:29 [notice] 1#1: start worker process 39
2025/06/18 04:48:29 [notice] 1#1: start worker process 40
2025/06/18 04:48:29 [notice] 1#1: start worker process 41
2025/06/18 04:48:29 [notice] 1#1: start worker process 42
2025/06/18 04:48:29 [notice] 1#1: start worker process 43
2025/06/18 04:48:29 [notice] 1#1: start worker process 44
2025/06/18 05:00:29 [notice] 1#1: using the "epoll" event method
2025/06/18 05:00:29 [notice] 1#1: nginx/1.25.5
2025/06/18 05:00:29 [notice] 1#1: built by gcc 13.2.1 20231014 (Alpine 13.2.1_git20231014) 
2025/06/18 05:00:29 [notice] 1#1: OS: Linux 6.6.87.2-microsoft-standard-WSL2
2025/06/18 05:00:29 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/18 05:00:29 [notice] 1#1: start worker processes
2025/06/18 05:00:29 [notice] 1#1: start worker process 29
2025/06/18 05:00:29 [notice] 1#1: start worker process 30
2025/06/18 05:00:29 [notice] 1#1: start worker process 31
2025/06/18 05:00:29 [notice] 1#1: start worker process 32
2025/06/18 05:00:29 [notice] 1#1: start worker process 33
2025/06/18 05:00:29 [notice] 1#1: start worker process 34
2025/06/18 05:00:29 [notice] 1#1: start worker process 35
2025/06/18 05:00:29 [notice] 1#1: start worker process 36
2025/06/18 05:00:29 [notice] 1#1: start worker process 37
2025/06/18 05:00:29 [notice] 1#1: start worker process 38
2025/06/18 05:00:29 [notice] 1#1: start worker process 39
2025/06/18 05:00:29 [notice] 1#1: start worker process 40
2025/06/18 05:00:29 [notice] 1#1: start worker process 41
2025/06/18 05:00:29 [notice] 1#1: start worker process 42
2025/06/18 05:00:29 [notice] 1#1: start worker process 43
2025/06/18 05:00:29 [notice] 1#1: start worker process 44
2025/06/19 01:31:20 [notice] 1#1: using the "epoll" event method
2025/06/19 01:31:20 [notice] 1#1: nginx/1.25.5
2025/06/19 01:31:20 [notice] 1#1: built by gcc 13.2.1 20231014 (Alpine 13.2.1_git20231014) 
2025/06/19 01:31:20 [notice] 1#1: OS: Linux 6.6.87.2-microsoft-standard-WSL2
2025/06/19 01:31:20 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
2025/06/19 01:31:20 [notice] 1#1: start worker processes
2025/06/19 01:31:20 [notice] 1#1: start worker process 29
2025/06/19 01:31:20 [notice] 1#1: start worker process 30
2025/06/19 01:31:20 [notice] 1#1: start worker process 31
2025/06/19 01:31:20 [notice] 1#1: start worker process 32
2025/06/19 01:31:20 [notice] 1#1: start worker process 33
2025/06/19 01:31:20 [notice] 1#1: start worker process 34
2025/06/19 01:31:20 [notice] 1#1: start worker process 35
2025/06/19 01:31:20 [notice] 1#1: start worker process 36
2025/06/19 01:31:20 [notice] 1#1: start worker process 37
2025/06/19 01:31:20 [notice] 1#1: start worker process 38
2025/06/19 01:31:20 [notice] 1#1: start worker process 39
2025/06/19 01:31:20 [notice] 1#1: start worker process 40
2025/06/19 01:31:20 [notice] 1#1: start worker process 41
2025/06/19 01:31:20 [notice] 1#1: start worker process 42
2025/06/19 01:31:20 [notice] 1#1: start worker process 43
2025/06/19 01:31:20 [notice] 1#1: start worker process 44
