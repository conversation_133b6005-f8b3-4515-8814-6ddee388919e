<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\InvitationRequest;
use App\Http\Resources\Api\V1\InvitationResource;
use App\Http\Resources\Api\V1\InvitationCollection;
use App\Models\Invitation;
use App\Services\InvitationService;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * Invitation Controller
 *
 * Handles invitation link functionality for users to join organizations with specific roles
 */
final class InvitationController extends ApiController
{
    public function __construct(
        private readonly PermissionService $permissionService,
        private readonly InvitationService $invitationService
    ) {
        // Set up automatic authorization for Invitation resource, but exclude show method
        $this->authorizeResource(Invitation::class, 'invitation', [
            'except' => ['show']
        ]);
    }

    /**
     * Display a listing of invitations
     * System admins can view all invitations, organization owners can view their own created invitations
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min((int) $request->get('per_page', 15), 100);

        $invitations = $this->invitationService->getInvitations(
            $request->user(),
            $perPage,
            $request->get('model_type'),
            $request->get('model_id') ? (int) $request->get('model_id') : null,
            $request->get('role'),
            $request->get('status')
        );

        return $this->successResponse(
            new InvitationCollection($invitations),
            'Invitations retrieved successfully'
        );
    }

    /**
     * Create a new invitation
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'model_type' => 'required|string|in:App\Models\Organisation',
            'model_id' => 'required|integer|exists:organisations,id',
            'role' => 'required|string|in:owner,member',
            'expires_at' => 'nullable|date|after:now',
            'max_uses' => 'nullable|integer|min:1|max:100',
            'email_restriction' => 'nullable|email',
        ]);

        try {
            $invitation = $this->invitationService->createInvitation(
                $request->validated(),
                $request->user()
            );

            return $this->successResponse(
                new InvitationResource($invitation->load(['model', 'createdBy'])),
                'Invitation created successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        }
    }

    /**
     * Display the specified invitation.
     * This method allows unauthenticated access to view invitation details, but requires authentication for accepting
     */
    public function show(Request $request, Invitation $invitation): JsonResponse
    {
        // Check if invitation is expired or reached usage limit - return 410 Gone
        if ($invitation->isExpired()) {
            return $this->errorResponse('Invitation link has expired', null, 410);
        }

        if ($invitation->hasReachedUsageLimit()) {
            return $this->errorResponse('Invitation link has reached usage limit', null, 410);
        }

        // Return full invitation information using resource
        return $this->successResponse(
            new InvitationResource($invitation->load(['model', 'createdBy'])),
            'Invitation information retrieved successfully'
        );
    }

    /**
     * Accept invitation and assign role to user
     * This is a custom action, so we need explicit authorization
     */
    public function accept(Request $request, Invitation $invitation): JsonResponse
    {
        $user = $request->user();

        if (!$invitation->isEmailAllowed($user->email)) {
            return $this->errorResponse('Your email address is not allowed to accept this invitation', null, 422);
        }

        $this->checkPermission('accept', $invitation);

        // Validate invitation before processing
        try {
            $this->invitationService->validateInvitationForAcceptance($invitation);
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        }

        // Check email restriction before policy authorization
        /*

        // Check system role restriction
        if ($invitation->isSystemRole()) {
            return $this->errorResponse('System role invitations cannot be accepted through this method', null, 403);
        }
        */

        try {
            $result = $this->invitationService->acceptInvitation($invitation, $user);

            return $this->successResponse(
                $result,
                'Invitation accepted successfully, joined organization'
            );
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), $e->getMessage());
        } catch (\Exception $e) {
            return $this->errorResponse('Error processing invitation: ' . $e->getMessage(), null, 500);
        }
    }

}
